"""
安装脚本 - 智能问答登录测试应用
"""
import subprocess
import sys
import os

def install_dependencies():
    """安装依赖包"""
    print("🔧 正在安装Python依赖包...")
    
    try:
        # 使用conda安装（用户偏好）
        subprocess.check_call([
            "conda", "install", "-y", 
            "requests", "gradio"
        ])
        
        # 使用pip安装conda中没有的包
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "pycryptodome", "gmssl", "dataclasses-json"
        ])
        
        print("✅ 依赖包安装完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        print("请手动运行以下命令:")
        print("conda install -y requests gradio")
        print("pip install pycryptodome gmssl dataclasses-json")
        return False
    except FileNotFoundError:
        print("⚠️  未找到conda，尝试使用pip安装...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ 依赖包安装完成!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ pip安装失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 智能问答登录测试应用 - 安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖
    if install_dependencies():
        print("\n🎉 安装完成!")
        print("\n📋 使用方法:")
        print("python gradio_qa_app.py")
        print("\n🌐 应用将在 http://localhost:7860 启动")
    else:
        print("\n❌ 安装失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
