{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false,
            "stopOnEntry": false
        },
        {
            "name": "Python: Gradio QA App",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/gradio_qa_app.py",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false,
            "stopOnEntry": false,
            "args": []
        },
        {
            "name": "Python: Debug with Arguments",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false,
            "stopOnEntry": false,
            "args": ["${input:commandLineArgs}"]
        },
        {
            "name": "Python: Attach to Process",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "localhost",
                "port": 5678
            },
            "justMyCode": false
        }
    ],
    "inputs": [
        {
            "id": "commandLineArgs",
            "description": "Command line arguments",
            "default": "",
            "type": "promptString"
        }
    ]
}