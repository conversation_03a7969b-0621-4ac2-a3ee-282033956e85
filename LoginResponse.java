package com.chinaunicom.datagov.metaapi.entity.aichat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 智能问答登录响应
 */
@Data
public class LoginResponse {
    /**
     * 请求是否成功
     */
    private Boolean success;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 结果数据
     */
    private Result result;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 登录结果
     */
    @Data
    public static class Result {
        /**
         * 会话是否更新
         */
        private Boolean sessionRenewal;
        
        /**
         * 令牌
         */
        private String token;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 真实姓名
         */
        private String realname;
    }
} 