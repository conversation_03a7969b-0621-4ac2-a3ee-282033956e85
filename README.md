# 智能问答登录测试 Gradio 应用

基于 `TestQARequest.java` 功能实现的 Python Gradio Web 应用，用于测试智能问答系统的登录功能。

## 🚀 功能特性

- ✅ **SM2加密**: 使用SM2算法加密用户数据
- ✅ **自动生成**: 自动生成UUID和当前日期
- ✅ **HTTP请求**: 发送登录请求到指定API端点
- ✅ **响应解析**: 解析并格式化显示登录响应
- ✅ **错误处理**: 详细的错误信息和异常处理
- ✅ **Web界面**: 友好的Gradio Web界面

## 📋 原始Java功能

该应用复现了以下Java代码的功能：

```java
// 创建用户数据
String data = "{\"user\":\""+user+"\", \"name\":\"许国杰\n\", \"orgCode\":\"数据处室\", \"orgName\":\"数据处室\", \"uuid\":\""+uuid+"\", \"day\":\""+day+"\"}";

// SM2加密
String encryptByPublicKey = SM2Utils.encryptByPublicKey(data, apiKey);

// 发送HTTP请求
String url = String.format("%s/touchSso/ssoLogin?touchId=%d&params=%s", 
    "http://***********:3900/api-gateway", Long.valueOf(appid), encryptByPublicKey);
```

## 🛠️ 安装和运行

### 方法1: 自动安装（推荐）

```bash
python setup.py
```

### 方法2: 手动安装

```bash
# 使用conda（推荐）
conda install -y requests gradio
pip install pycryptodome gmssl dataclasses-json

# 或使用pip
pip install -r requirements.txt
```

### 启动应用

```bash
python gradio_qa_app.py
```

应用将在 http://localhost:7860 启动

## 📁 项目结构

```
.
├── gradio_qa_app.py      # 主应用文件
├── requirements.txt      # Python依赖
├── setup.py             # 安装脚本
├── README.md            # 说明文档
└── utils/               # 工具模块
    ├── __init__.py
    ├── sm2_utils.py     # SM2加密工具
    └── login_response.py # 响应数据类
```

## 🔧 使用说明

1. **API密钥**: 输入SM2公钥用于数据加密
2. **用户名**: 输入登录用户标识
3. **应用ID**: 输入应用程序标识符  
4. **Touch ID**: 输入触摸登录标识符
5. 点击"开始测试"按钮执行登录测试

## 📊 输出信息

应用提供三个标签页显示结果：

- **响应结果**: 格式化的登录响应信息
- **加密数据**: SM2加密后的用户数据
- **请求URL**: 完整的HTTP请求URL

## ⚙️ 配置参数

默认配置（来自原Java代码）：

```python
API密钥: "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEuuet5IYtshC+FxXAnTgCGPbU0yyozb9QNVwqLYejGRYBzKRWJmCEmcferOnSDSKt58tvJVugs7l1ILF62Z9y3A=="
用户名: "shs-xxaqbzylhy"
应用ID: "1745223746865"
Touch ID: "1745223746865"
API端点: "http://***********:3900/api-gateway/touchSso/ssoLogin"
```

## 🔒 安全说明

- SM2加密确保用户数据传输安全
- 应用仅用于测试目的
- 请妥善保管API密钥和用户凭据

## 🐛 故障排除

### 常见问题

1. **SM2加密失败**
   - 检查API密钥格式是否正确
   - 确保gmssl库正确安装

2. **网络请求失败**
   - 检查网络连接
   - 确认API端点地址可访问

3. **依赖安装失败**
   - 尝试更新pip: `pip install --upgrade pip`
   - 使用conda安装: `conda install -c conda-forge gmssl`

## 📝 开发说明

该应用使用以下主要技术：

- **Gradio**: Web界面框架
- **gmssl**: SM2加密算法实现
- **requests**: HTTP请求库
- **dataclasses**: 数据类定义

## 📄 许可证

本项目仅供学习和测试使用。
