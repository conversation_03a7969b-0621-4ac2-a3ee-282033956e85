"""
SM2加密工具类
使用gmssl库实现SM2加密功能
"""
import base64
from typing import Optional

try:
    from gmssl import sm2
    GMSSL_AVAILABLE = True
except ImportError:
    GMSSL_AVAILABLE = False
    print("警告: gmssl库未正确安装，将使用模拟加密")

class SM2Utils:
    """SM2加密工具类"""

    @staticmethod
    def encrypt_by_public_key(data: str, public_key: str) -> Optional[str]:
        """
        使用公钥加密数据

        Args:
            data: 要加密的数据
            public_key: 公钥字符串

        Returns:
            加密后的base64编码字符串，失败返回None
        """
        if not GMSSL_AVAILABLE:
            # 模拟加密 - 仅用于演示
            print("使用模拟加密（仅用于演示）")
            mock_encrypted = base64.b64encode(f"MOCK_ENCRYPTED_{data}".encode('utf-8')).decode('utf-8')
            return mock_encrypted

        try:
            # 解码公钥
            public_key_bytes = base64.b64decode(public_key)

            # 提取公钥坐标 (跳过前面的标识字节)
            # SM2公钥格式通常是04 + 32字节x坐标 + 32字节y坐标
            if len(public_key_bytes) >= 65 and public_key_bytes[0] == 0x04:
                x = public_key_bytes[1:33].hex()
                y = public_key_bytes[33:65].hex()
            else:
                # 尝试其他格式解析
                key_hex = public_key_bytes.hex()
                if len(key_hex) >= 128:
                    x = key_hex[:64]
                    y = key_hex[64:128]
                else:
                    raise ValueError("无效的公钥格式")

            # 创建SM2加密对象
            sm2_crypt = sm2.CryptSM2(public_key=x+y, private_key="")

            # 加密数据
            encrypted_data = sm2_crypt.encrypt(data.encode('utf-8'))

            # 返回base64编码的结果
            return base64.b64encode(encrypted_data).decode('utf-8')

        except Exception as e:
            print(f"SM2加密失败: {str(e)}")
            return None

    @staticmethod
    def decrypt_by_private_key(encrypted_data: str, private_key: str) -> Optional[str]:
        """
        使用私钥解密数据

        Args:
            encrypted_data: base64编码的加密数据
            private_key: 私钥字符串

        Returns:
            解密后的字符串，失败返回None
        """
        try:
            # 解码加密数据
            encrypted_bytes = base64.b64decode(encrypted_data)

            # 创建SM2解密对象
            sm2_crypt = sm2.CryptSM2(public_key="", private_key=private_key)

            # 解密数据
            decrypted_data = sm2_crypt.decrypt(encrypted_bytes)

            return decrypted_data.decode('utf-8')

        except Exception as e:
            print(f"SM2解密失败: {str(e)}")
            return None
